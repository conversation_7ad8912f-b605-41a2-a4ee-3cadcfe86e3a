import React from 'react';
import { PersonalizedDashboard } from '@/components/dashboard/PersonalizedDashboard';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import TopNav from '@/components/layout/TopNav';

function Dashboard() {
  return (
    <ProtectedRoute requiredPermissions={[{ resourceType: 'dashboard', action: 'view' }]}>
      <div className="min-h-screen bg-background">
        <TopNav />
        <main className="container mx-auto px-4 py-8 pt-20">
          <PersonalizedDashboard />
        </main>
      </div>
    </ProtectedRoute>
  );
}

export default Dashboard;
              <Skeleton className="h-7 w-20" />
            ) : (
              <div className="text-2xl font-bold">
                {card.isCurrency ? formatCurrency(card.value) : formatNumber(card.value)}
              </div>
            )}
            <div className="text-xs text-muted-foreground mt-1">
              {isLoading ? (
                <Skeleton className="h-4 w-32" />
              ) : (
                <div className="flex items-center gap-1">
                  <TrendingUp size={12} />
                  <span>{card.subtitle}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

// Componente de gráficos
const DashboardCharts = ({ 
  graficoStatusPrecatorios, 
  graficoValorPorMes, 
  sectionLoading, 
  sectionErrors,
  canViewFinancialData
}) => {
  // Preparar dados para gráfico de status
  const statusChartData = useMemo(() => {
    if (!graficoStatusPrecatorios || !Array.isArray(graficoStatusPrecatorios) || graficoStatusPrecatorios.length === 0) {
      return {
        labels: [],
        datasets: []
      };
    }
    
    return {
      labels: graficoStatusPrecatorios.map(item => item.status.replace('_', ' ')),
      datasets: [
        {
          label: 'Precatórios por Status',
          data: graficoStatusPrecatorios.map(item => item.quantidade),
          backgroundColor: [
            chartColors.blue,
            chartColors.green,
            chartColors.yellow,
            chartColors.purple,
            chartColors.red,
            chartColors.gray,
          ],
          borderWidth: 1
        }
      ]
    };
  }, [graficoStatusPrecatorios]);
  
  // Preparar dados para gráfico de valor por mês
  const valorChartData = useMemo(() => {
    if (!graficoValorPorMes || !Array.isArray(graficoValorPorMes) || graficoValorPorMes.length === 0) {
      return {
        labels: [],
        datasets: []
      };
    }
    
    return {
      labels: graficoValorPorMes.map(item => item.mes),
      datasets: [
        {
          label: 'Valor Total (R$)',
          data: graficoValorPorMes.map(item => item.valor),
          borderColor: chartColors.green,
          backgroundColor: chartColors.lightGreen,
          tension: 0.4,
          fill: true
        }
      ]
    };
  }, [graficoValorPorMes]);
  
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart size={18} />
            Precatórios por Status
          </CardTitle>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center">
          {sectionLoading.graficoStatus ? (
            <div className="flex flex-col items-center gap-2">
              <Skeleton className="h-[200px] w-[200px] rounded-full" />
              <Skeleton className="h-4 w-32" />
            </div>
          ) : sectionErrors.graficoStatus ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Erro</AlertTitle>
              <AlertDescription>{sectionErrors.graficoStatus}</AlertDescription>
            </Alert>
          ) : statusChartData.labels.length === 0 ? (
            <div className="text-center text-muted-foreground">
              Nenhum dado disponível
            </div>
          ) : (
            <div className="w-full h-full">
              <Doughnut 
                data={statusChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'right',
                      labels: {
                        boxWidth: 12,
                        padding: 15
                      }
                    },
                    tooltip: {
                      callbacks: {
                        label: function(context) {
                          const label = context.label || '';
                          const value = context.raw || 0;
                          const total = context.dataset.data.reduce((a, b) => a + b, 0);
                          const percentage = Math.round((value / total) * 100);
                          return `${label}: ${value} (${percentage}%)`;
                        }
                      }
                    }
                  }
                }}
              />
            </div>
          )}
        </CardContent>
      </Card>
      
      <DataVisibilityGuard requiredPermission="visualizar_relatorios">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 size={18} />
              Evolução de Valores
            </CardTitle>
          </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center">
            {sectionLoading.graficoValor ? (
              <div className="flex flex-col w-full gap-2">
                <Skeleton className="h-[200px] w-full" />
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-4 w-12" />
                </div>
              </div>
            ) : sectionErrors.graficoValor ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Erro</AlertTitle>
                <AlertDescription>{sectionErrors.graficoValor}</AlertDescription>
              </Alert>
            ) : valorChartData.labels.length === 0 ? (
              <div className="text-center text-muted-foreground">
                Nenhum dado disponível
              </div>
            ) : (
              <div className="w-full h-full">
                <Line 
                  data={valorChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: false
                      },
                      tooltip: {
                        callbacks: {
                          label: function(context) {
                            const label = context.dataset.label || '';
                            const value = context.raw || 0;
                            return `${label}: ${formatCurrency(value)}`;
                          }
                        }
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          callback: function(value) {
                            return formatCurrency(value, { compact: true });
                          }
                        }
                      }
                    }
                  }}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </DataVisibilityGuard>
    </div>
  );
};

// Componente de listas recentes
const RecentItems = ({ 
  precatoriosRecentes, 
  tarefasRecentes, 
  sectionLoading, 
  sectionErrors 
}) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText size={18} />
            Precatórios Recentes
          </CardTitle>
        </CardHeader>
        <CardContent>
          {sectionLoading.precatoriosRecentes ? (
            <div className="space-y-3">
              {Array(5).fill(0).map((_, i) => (
                <div key={i} className="flex items-center gap-3">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-1.5 flex-1">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </div>
              ))}
            </div>
          ) : sectionErrors.precatoriosRecentes ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Erro</AlertTitle>
              <AlertDescription>{sectionErrors.precatoriosRecentes}</AlertDescription>
            </Alert>
          ) : !precatoriosRecentes || precatoriosRecentes.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              Nenhum precatório recente encontrado
            </div>
          ) : (
            <ScrollArea className="h-[300px] pr-4">
              <div className="space-y-4">
                {precatoriosRecentes.map((precatorio) => (
                  <div key={precatorio.id} className="flex items-start gap-3">
                    <div className={`w-3 h-3 mt-1.5 rounded-full ${statusColors[precatorio.status] || 'bg-gray-500'}`} />
                    <div className="flex-1">
                      <div className="font-medium">{precatorio.numero || 'Sem número'}</div>
                      <div className="text-sm text-muted-foreground flex items-center gap-2">
                        <span>{precatorio.cliente?.nome || 'Cliente não identificado'}</span>
                        <span className="text-xs">•</span>
                        <span>{formatDate(precatorio.created_at)}</span>
                      </div>
                    </div>
                    <Badge variant="outline">{formatCurrency(precatorio.valor || 0)}</Badge>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
        <CardFooter className="pt-0">
          <Button variant="ghost" size="sm" className="ml-auto">
            Ver todos
            <ArrowUpRight size={14} className="ml-1" />
          </Button>
        </CardFooter>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock size={18} />
            Tarefas Recentes
          </CardTitle>
        </CardHeader>
        <CardContent>
          {sectionLoading.tarefasRecentes ? (
            <div className="space-y-3">
              {Array(5).fill(0).map((_, i) => (
                <div key={i} className="flex items-center gap-3">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-1.5 flex-1">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </div>
              ))}
            </div>
          ) : sectionErrors.tarefasRecentes ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Erro</AlertTitle>
              <AlertDescription>{sectionErrors.tarefasRecentes}</AlertDescription>
            </Alert>
          ) : !tarefasRecentes || tarefasRecentes.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              Nenhuma tarefa recente encontrada
            </div>
          ) : (
            <ScrollArea className="h-[300px] pr-4">
              <div className="space-y-4">
                {tarefasRecentes.map((tarefa) => (
                  <div key={tarefa.id} className="flex items-start gap-3">
                    <div className={`w-3 h-3 mt-1.5 rounded-full ${
                      tarefa.status === 'concluida' ? 'bg-green-500' : 
                      tarefa.status === 'atrasada' ? 'bg-red-500' : 
                      'bg-yellow-500'
                    }`} />
                    <div className="flex-1">
                      <div className="font-medium">{tarefa.titulo}</div>
                      <div className="text-sm text-muted-foreground flex items-center gap-2">
                        <span>{tarefa.responsavel?.nome || 'Sem responsável'}</span>
                        <span className="text-xs">•</span>
                        <span>
                          {tarefa.data_vencimento ? formatDate(tarefa.data_vencimento) : 'Sem prazo'}
                        </span>
                      </div>
                    </div>
                    <Badge variant={tarefa.status === 'concluida' ? 'default' : 'outline'}>
                      {tarefa.status === 'concluida' ? 'Concluída' : 
                       tarefa.status === 'atrasada' ? 'Atrasada' : 'Pendente'}
                    </Badge>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
        <CardFooter className="pt-0">
          <Button variant="ghost" size="sm" className="ml-auto">
            Ver todas
            <ArrowUpRight size={14} className="ml-1" />
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

// Componente de desempenho da equipe
const TeamPerformance = ({ 
  desempenhoEquipe, 
  sectionLoading, 
  sectionErrors,
  canViewTeamPerformance
}) => {
  if (!canViewTeamPerformance) {
    return null;
  }
  
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users size={18} />
          Desempenho da Equipe
        </CardTitle>
        <CardDescription>
          Métricas de desempenho dos membros da equipe no período selecionado
        </CardDescription>
      </CardHeader>
      <CardContent>
        {sectionLoading.desempenhoEquipe ? (
          <div className="space-y-4">
            {Array(4).fill(0).map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <Skeleton className="h-4 w-40" />
                  <div className="ml-auto flex gap-2">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
                <Skeleton className="h-2 w-full" />
              </div>
            ))}
          </div>
        ) : sectionErrors.desempenhoEquipe ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erro</AlertTitle>
            <AlertDescription>{sectionErrors.desempenhoEquipe}</AlertDescription>
          </Alert>
        ) : !desempenhoEquipe || desempenhoEquipe.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            Nenhum dado de desempenho disponível
          </div>
        ) : (
          <div className="space-y-6">
            {desempenhoEquipe.map((membro) => {
              const totalTarefas = membro.concluidos + membro.pendentes;
              const porcentagem = totalTarefas > 0 
                ? Math.round((membro.concluidos / totalTarefas) * 100) 
                : 0;
                
              return (
                <div key={membro.usuario} className="space-y-2">
                  <div className="flex items-center">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                        {membro.foto_url ? (
                          <img 
                            src={membro.foto_url} 
                            alt={membro.usuario} 
                            className="w-10 h-10 rounded-full object-cover"
                          />
                        ) : (
                          <Users size={20} className="text-primary" />
                        )}
                      </div>
                      <div>
                        <div className="font-medium">{membro.usuario}</div>
                        <div className="text-xs text-muted-foreground">{membro.cargo || 'Sem cargo'}</div>
                      </div>
                    </div>
                    <div className="ml-auto flex gap-4 text-sm">
                      <div>
                        <span className="font-medium">{membro.concluidos}</span>
                        <span className="text-muted-foreground ml-1">concluídas</span>
                      </div>
                      <div>
                        <span className="font-medium">{membro.pendentes}</span>
                        <span className="text-muted-foreground ml-1">pendentes</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <Progress value={porcentagem} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>{porcentagem}% concluído</span>
                      <span>{totalTarefas} tarefas totais</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Componente de notificações
const DashboardNotifications = ({ data, isLoading }) => {
  const [showNotifications, setShowNotifications] = useState(false);
  
  const notificacoes = useMemo(() => {
    if (!data) return [];
    
    const result = [];
    
    // Precatórios atrasados
    if (data.precatoriosAtrasados && data.precatoriosAtrasados > 0) {
      result.push({
        id: 'precatorios-atrasados',
        titulo: `${data.precatoriosAtrasados} precatórios atrasados`,
        descricao: 'Precatórios que passaram do prazo estimado',
        tipo: 'warning',
        icon: <AlertCircle size={16} className="text-yellow-500" />
      });
    }
    
    // Tarefas atrasadas
    if (data.tarefasAtrasadas && data.tarefasAtrasadas > 0) {
      result.push({
        id: 'tarefas-atrasadas',
        titulo: `${data.tarefasAtrasadas} tarefas atrasadas`,
        descricao: 'Tarefas que passaram do prazo de conclusão',
        tipo: 'error',
        icon: <AlertCircle size={16} className="text-red-500" />
      });
    }
    
    // Precatórios sem responsável
    if (data.precatoriosSemResponsavel && data.precatoriosSemResponsavel > 0) {
      result.push({
        id: 'precatorios-sem-responsavel',
        titulo: `${data.precatoriosSemResponsavel} precatórios sem responsável`,
        descricao: 'Precatórios que precisam ser atribuídos a um responsável',
        tipo: 'info',
        icon: <Users size={16} className="text-blue-500" />
      });
    }
    
    return result;
  }, [data]);
  
  if (notificacoes.length === 0 && !isLoading) {
    return null;
  }
  
  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center gap-2">
            <Bell size={18} />
            Notificações
          </CardTitle>
          <Badge variant="outline" className="ml-2">
            {isLoading ? '...' : notificacoes.length}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            {Array(2).fill(0).map((_, i) => (
              <div key={i} className="flex items-center gap-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="space-y-1.5 flex-1">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        ) : notificacoes.length === 0 ? (
          <div className="text-center py-2 text-muted-foreground">
            Nenhuma notificação pendente
          </div>
        ) : (
          <div className="space-y-3">
            {notificacoes.slice(0, showNotifications ? notificacoes.length : 3).map((notificacao) => (
              <div key={notificacao.id} className="flex items-start gap-3 p-3 rounded-lg bg-muted/50">
                <div className="mt-0.5">{notificacao.icon}</div>
                <div>
                  <div className="font-medium">{notificacao.titulo}</div>
                  <div className="text-sm text-muted-foreground">{notificacao.descricao}</div>
                </div>
              </div>
            ))}
            
            {notificacoes.length > 3 && (
              <Button 
                variant="ghost" 
                size="sm" 
                className="w-full text-center"
                onClick={() => setShowNotifications(!showNotifications)}
              >
                {showNotifications ? 'Mostrar menos' : `Ver mais ${notificacoes.length - 3} notificações`}
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Componente principal do Dashboard
const Dashboard = () => {
  const { user, isAdmin, isGerente } = useAuth();
  const { hasPermission } = usePermissions();
  
  const {
    // Dados
    data,
    stats,
    graficoStatusPrecatorios,
    graficoValorPorMes,
    precatoriosRecentes,
    tarefasRecentes,
    desempenhoEquipe,
    
    // Estado
    isLoading,
    sectionLoading,
    error,
    sectionErrors,
    
    // Filtros
    filters,
    setFilters,
    
    // Ações
    refreshData,
    clearCache,
    
    // Permissões
    canViewFinancialData,
    canViewTeamPerformance
  } = useDashboardData();
  
  // Determinar qual dashboard mostrar com base no papel do usuário
  const dashboardType = useMemo(() => {
    if (isAdmin) return 'admin';
    if (isGerente) return 'gerente';
    if (user?.role?.includes('operacional')) return 'operacional';
    if (user?.role === 'captador') return 'captador';
    return 'basico';
  }, [isAdmin, isGerente, user?.role]);

  // Definir título baseado no tipo de dashboard
  const dashboardTitle = useMemo(() => {
    switch (dashboardType) {
      case 'admin':
        return 'Dashboard Administrativo';
      case 'gerente':
        return 'Dashboard Gerencial';
      case 'operacional':
        return 'Dashboard Operacional';
      case 'captador':
        return 'Dashboard de Captação';
      default:
        return 'Dashboard';
    }
  }, [dashboardType]);

  return (
    <div className="min-h-screen bg-background">
      <TopNav
        title={dashboardTitle}
        actions={
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => refreshData()}
              disabled={isLoading}
            >
              <RefreshCw
                size={16}
                className={`mr-2 ${isLoading ? 'animate-spin' : ''}`}
              />
              Atualizar
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearCache}
              disabled={isLoading}
            >
              Limpar Cache
            </Button>
          </div>
        }
      />

      <div className="container mx-auto py-6 px-4 sm:px-6 pt-20">
      
      {/* Mensagem de boas-vindas personalizada */}
      <Card className="mb-6 bg-gradient-to-r from-muted/50 to-background border-0">
        <CardContent className="pt-6">
          <h2 className="text-2xl font-semibold mb-2">
            Olá, {user?.nome || 'Usuário'}!
          </h2>
          <p className="text-muted-foreground">
            {dashboardType === 'admin' && 'Aqui está uma visão geral completa do sistema e desempenho da equipe.'}
            {dashboardType === 'gerente' && 'Aqui está uma visão geral dos precatórios e desempenho da sua equipe.'}
            {dashboardType === 'operacional' && 'Aqui está um resumo dos seus precatórios e tarefas pendentes.'}
            {dashboardType === 'basico' && 'Aqui está um resumo das informações disponíveis para você.'}
          </p>
        </CardContent>
      </Card>
      
      {/* Notificações */}
      <DashboardNotifications data={data} isLoading={isLoading} />
      
      {/* Filtros */}
      <DashboardFilters 
        filters={filters} 
        setFilters={setFilters} 
        isLoading={isLoading} 
      />
      
      {/* Cards de estatísticas */}
      <StatsCards
        stats={stats}
        isLoading={sectionLoading.stats}
        error={sectionErrors.stats}
        dashboardType={dashboardType}
      />
      
      {/* Gráficos */}
      <DashboardCharts 
        graficoStatusPrecatorios={graficoStatusPrecatorios}
        graficoValorPorMes={graficoValorPorMes}
        sectionLoading={sectionLoading}
        sectionErrors={sectionErrors}
        canViewFinancialData={canViewFinancialData}
      />
      
      {/* Listas recentes */}
      <RecentItems 
        precatoriosRecentes={precatoriosRecentes}
        tarefasRecentes={tarefasRecentes}
        sectionLoading={sectionLoading}
        sectionErrors={sectionErrors}
      />
      
      {/* Desempenho da equipe (apenas para admin e gerentes) */}
      <TeamPerformance 
        desempenhoEquipe={desempenhoEquipe}
        sectionLoading={sectionLoading}
        sectionErrors={sectionErrors}
        canViewTeamPerformance={canViewTeamPerformance}
      />
      
      {/* Seção de metas e progresso */}
      <DataVisibilityGuard requiredPermission="visualizar_relatorios">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp size={18} />
              Metas e Progresso
            </CardTitle>
            <CardDescription>
              Acompanhamento das metas estabelecidas para o período
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-6">
                {Array(3).fill(0).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-40" />
                      <Skeleton className="h-4 w-20" />
                    </div>
                    <Skeleton className="h-2 w-full" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-6">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <div className="font-medium">Precatórios Processados</div>
                    <div className="text-sm">
                      {data?.metaAtingida || 0} de {data?.metaTotal || 100}
                    </div>
                  </div>
                  <Progress 
                    value={data?.metaAtingida ? (data.metaAtingida / data.metaTotal) * 100 : 0} 
                    className="h-2" 
                  />
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <div className="font-medium">Tempo Médio de Processamento</div>
                    <div className="text-sm">
                      {data?.tempoMedioConclusao?.toFixed(1) || 0} dias
                    </div>
                  </div>
                  <Progress 
                    value={
                      data?.tempoMedioConclusao && data?.tempoMedioMeta
                        ? Math.max(0, 100 - (data.tempoMedioConclusao / data.tempoMedioMeta) * 100)
                        : 0
                    } 
                    className="h-2" 
                  />
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <div className="font-medium">Satisfação do Cliente</div>
                    <div className="text-sm">
                      {data?.satisfacaoCliente || 0}%
                    </div>
                  </div>
                  <Progress 
                    value={data?.satisfacaoCliente || 0} 
                    className="h-2" 
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </DataVisibilityGuard>
      </div>
    </div>
  );
};

export default Dashboard;
